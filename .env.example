# Supabase Configuration
VITE_SUPABASE_URL=https://exxscdqyluftxhtxdofq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2wsYufp3xqO1gUY08fT5VhXxH3z4X1Ihpm6C1IPvetk

# Development
NODE_ENV=development

# Optional: OpenAI API Key for Supabase AI features
# OPENAI_API_KEY=your_openai_api_key_here

# Optional: SMS Provider Configuration (if using SMS auth)
# SUPABASE_AUTH_SMS_TWILIO_AUTH_TOKEN=your_twilio_auth_token

# Optional: External OAuth Provider Secrets (if using OAuth)
# SUPABASE_AUTH_EXTERNAL_APPLE_SECRET=your_apple_oauth_secret
# SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET=your_google_oauth_secret
# SUPABASE_AUTH_EXTERNAL_GITHUB_SECRET=your_github_oauth_secret

# Optional: S3 Configuration (if using experimental S3 features)
# S3_HOST=your_s3_bucket.s3-region.amazonaws.com
# S3_REGION=us-east-1
# S3_ACCESS_KEY=your_aws_access_key
# S3_SECRET_KEY=your_aws_secret_key
