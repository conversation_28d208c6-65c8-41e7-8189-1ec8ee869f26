-- Fix missing constraints and complete the schema setup

-- Add unique constraint to moto_taxi_drivers if it doesn't exist
do $$
begin
  if not exists (
    select 1 from pg_constraint 
    where conname = 'moto_taxi_drivers_name_phone_key'
  ) then
    alter table public.moto_taxi_drivers 
    add constraint moto_taxi_drivers_name_phone_key unique (name, phone);
  end if;
end $$;

-- Now insert the seed data for moto taxi drivers
insert into public.moto_taxi_drivers (
  name, phone, whatsapp, vehicle_type, passenger_capacity, years_experience, 
  areas_covered, price_range, available_hours, accepts_pix, accepts_cash, 
  rating, total_reviews, is_available, languages
)
values
('<PERSON>','91987654321','91987654321','motorcycle',1,5,ARRAY['Todas as praias','Centro'],'R$ 5-10','06:00-22:00',true,true,4.8,23,true,ARRAY['pt']),
('<PERSON>','91988776655','91988776655','motorrete',6,8,<PERSON><PERSON><PERSON>['<PERSON><PERSON><PERSON> <PERSON>','<PERSON><PERSON><PERSON>','Far<PERSON>'],'R$ 10-20','07:00-20:00',true,false,4.9,45,true,ARRAY['pt']),
('<PERSON> <PERSON>','91987665544','91987665544','motorcycle',1,3,ARRAY['Vai-Quem-Quer','Centro'],'R$ 5-8','08:00-18:00',false,true,4.6,15,false,ARRAY['pt']),
('Paulo Santos','91988990011','91988990011','motorrete',4,10,ARRAY['Todas as áreas'],'R$ 15-25','06:00-23:00',true,true,5.0,67,true,ARRAY['pt','en']),
('Miguel Costa','91987223344','91987223344','motorcycle',1,1,ARRAY['Praia do Amor','Centro'],'R$ 5-10','09:00-19:00',true,true,0.0,0,true,ARRAY['pt']),
('André Lima','91988334455','91988334455','motorcycle',1,7,ARRAY['Flexeira','Farol','Vai-Quem-Quer'],'R$ 8-12','07:00-21:00',true,false,4.7,31,true,ARRAY['pt']),
('Francisco Oliveira','91987556677','91987556677','motorrete',6,12,ARRAY['Todas as praias','Ruínas','Centro'],'R$ 12-20','06:00-22:00',true,true,4.9,89,false,ARRAY['pt']),
('João Pedro','91988445566','91988445566','motorcycle',1,4,ARRAY['Centro','Terminal'],'R$ 5-8','10:00-18:00',false,true,4.5,12,true,ARRAY['pt']),
('Ana Beatriz','91988112233','91988112233','motorcycle',1,6,ARRAY['Farol','Flexeira'],'R$ 7-12','07:00-19:00',true,true,4.7,28,true,ARRAY['pt','en']),
('Marcos Paulo','91988223344','91988223344','motorrete',6,9,ARRAY['Vai-Quem-Quer','Amor'],'R$ 12-22','08:00-21:00',true,true,4.8,54,true,ARRAY['pt']),
('Diego Souza','91988335544','91988335544','motorcycle',1,2,ARRAY['Centro'],'R$ 5-9','08:00-17:00',true,true,4.2,8,true,ARRAY['pt']),
('Pedro Henrique','91988446655','91988446655','motorrete',6,11,ARRAY['Todas as áreas'],'R$ 14-24','06:00-22:00',true,false,4.9,72,true,ARRAY['pt']),
('Rafael Lima','91988557766','91988557766','motorcycle',1,3,ARRAY['Amor','Flexeira'],'R$ 6-10','09:00-18:00',true,true,4.3,10,false,ARRAY['pt']),
('Felipe Alves','91988668877','91988668877','motorrete',5,5,ARRAY['Centro','Terminal'],'R$ 12-18','07:00-20:00',true,true,4.5,20,true,ARRAY['pt','en']),
('Lucas Gabriel','91988779988','91988779988','motorcycle',1,7,ARRAY['Farol','Vai-Quem-Quer'],'R$ 8-12','06:00-20:00',true,true,4.6,33,true,ARRAY['pt']),
('Bruno Carvalho','91988880011','91988880011','motorrete',6,6,ARRAY['Flexeira','Centro'],'R$ 13-21','08:00-22:00',true,true,4.4,18,true,ARRAY['pt'])
on conflict (name, phone) do nothing;
