-- Consolidated migration to fix schema issues and establish clean baseline
-- This migration consolidates all previous migrations into a single, clean schema

-- Enable necessary extensions
create extension if not exists "uuid-ossp";

-- ============================================================================
-- PROFILES TABLE
-- ============================================================================

-- Profiles table and trigger
create table if not exists public.profiles (
  id uuid primary key references auth.users(id) on delete cascade,
  display_name text,
  avatar_url text,
  member_since timestamptz not null default now()
);

alter table public.profiles enable row level security;

-- Drop existing policies to recreate
drop policy if exists "Profiles are viewable by everyone" on public.profiles;
drop policy if exists "Public can view profiles" on public.profiles;
drop policy if exists "Users can update own profile" on public.profiles;
drop policy if exists "Users can insert own profile" on public.profiles;
drop policy if exists "Users can delete own profile" on public.profiles;

-- Enhanced profile policies
create policy "Public can view profiles"
  on public.profiles for select using (true);

create policy "Users can insert own profile"
  on public.profiles for insert to authenticated 
  with check (auth.uid() = id);

create policy "Users can update own profile"
  on public.profiles for update to authenticated 
  using (auth.uid() = id)
  with check (auth.uid() = id);

create policy "Users can delete own profile"
  on public.profiles for delete to authenticated 
  using (auth.uid() = id);

-- Trigger to insert profile on signup
create or replace function public.handle_new_user()
returns trigger
language plpgsql
security definer set search_path = public
as $$
begin
  insert into public.profiles (id, display_name, avatar_url)
  values (new.id, coalesce(new.raw_user_meta_data->>'name', 'Usuário'), new.raw_user_meta_data->>'avatar_url')
  on conflict (id) do nothing;
  return new;
end;
$$;

drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- ============================================================================
-- MOTO TAXI DRIVERS TABLE
-- ============================================================================

-- Moto Taxi Drivers table with proper constraints
create table if not exists public.moto_taxi_drivers (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  phone text not null,
  whatsapp text,
  vehicle_type text check (vehicle_type in ('motorcycle','motorrete')) not null,
  vehicle_model text,
  license_plate text,
  passenger_capacity integer default 1,
  photo_url text,
  areas_covered text[],
  languages text[] default array['pt'],
  years_experience integer,
  rating numeric(3,2) default 0,
  total_rides integer default 0,
  total_reviews integer default 0,
  price_range text,
  available_hours text,
  accepts_pix boolean default true,
  accepts_cash boolean default true,
  is_verified boolean default false,
  is_available boolean default true,
  notes text,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  -- Add unique constraint to prevent duplicates
  unique(name, phone)
);

alter table public.moto_taxi_drivers enable row level security;

-- Drop existing policies
drop policy if exists "Public can view drivers" on public.moto_taxi_drivers;
drop policy if exists "Public can view active drivers" on public.moto_taxi_drivers;

-- Enhanced moto taxi driver policies
create policy "Public can view active drivers"
  on public.moto_taxi_drivers for select 
  using (is_available = true or is_verified = true);

-- ============================================================================
-- BEACH LOCATIONS TABLE
-- ============================================================================

-- Beach locations table
create table if not exists public.beach_locations (
  id uuid primary key default gen_random_uuid(),
  slug text unique not null,
  name text not null,
  description text,
  full_description text,
  coordinates text,
  latitude numeric(10, 8),
  longitude numeric(11, 8),
  amenities text[],
  how_to_get text,
  tips text[],
  is_featured boolean default false,
  is_active boolean default true,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

alter table public.beach_locations enable row level security;

-- Drop existing policies
drop policy if exists "Public can view active locations" on public.beach_locations;

-- Public can view active locations
create policy "Public can view active locations"
  on public.beach_locations for select using (is_active = true);

-- ============================================================================
-- USER PHOTOS TABLE
-- ============================================================================

-- User photos table
create table if not exists public.user_photos (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  image_url text not null,
  thumbnail_url text,
  caption text,
  location_id uuid references public.beach_locations(id),
  location_name text,
  tags text[],
  likes_count integer default 0,
  is_featured boolean default false,
  is_approved boolean default false,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

alter table public.user_photos enable row level security;

-- Drop existing policies
drop policy if exists "Public can view approved photos" on public.user_photos;
drop policy if exists "Users can insert own photos" on public.user_photos;
drop policy if exists "Authenticated users can insert own photos" on public.user_photos;
drop policy if exists "Users can update own photos" on public.user_photos;
drop policy if exists "Users can delete own photos" on public.user_photos;

-- Enhanced photo policies
create policy "Public can view approved photos"
  on public.user_photos for select 
  using (is_approved = true or auth.uid() = user_id);

create policy "Authenticated users can insert own photos"
  on public.user_photos for insert to authenticated 
  with check (auth.uid() = user_id and is_approved = false);

create policy "Users can update own photos"
  on public.user_photos for update to authenticated 
  using (auth.uid() = user_id)
  with check (auth.uid() = user_id);

create policy "Users can delete own photos"
  on public.user_photos for delete to authenticated 
  using (auth.uid() = user_id);

-- ============================================================================
-- PHOTO LIKES TABLE
-- ============================================================================

-- Photo likes table
create table if not exists public.photo_likes (
  id uuid primary key default gen_random_uuid(),
  photo_id uuid not null references public.user_photos(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  created_at timestamptz default now(),
  unique(photo_id, user_id)
);

alter table public.photo_likes enable row level security;

-- Drop existing policies
drop policy if exists "Anyone can read likes" on public.photo_likes;
drop policy if exists "Users can like" on public.photo_likes;
drop policy if exists "Authenticated users can like photos" on public.photo_likes;
drop policy if exists "Users can unlike own like" on public.photo_likes;
drop policy if exists "Users can unlike own likes" on public.photo_likes;

-- Enhanced photo likes policies
create policy "Anyone can read likes"
  on public.photo_likes for select using (true);

create policy "Authenticated users can like photos"
  on public.photo_likes for insert to authenticated 
  with check (auth.uid() = user_id);

create policy "Users can unlike own likes"
  on public.photo_likes for delete to authenticated 
  using (auth.uid() = user_id);

-- Prevent duplicate likes
create unique index if not exists photo_likes_user_photo_unique 
  on public.photo_likes(photo_id, user_id);

-- ============================================================================
-- STORAGE SETUP
-- ============================================================================

-- Storage bucket for user photos
insert into storage.buckets (id, name, public)
values ('user-photos','user-photos', true)
on conflict (id) do nothing;

-- Drop existing storage policies
drop policy if exists "Public can view user photos" on storage.objects;
drop policy if exists "Users can upload to their folder" on storage.objects;
drop policy if exists "Authenticated users can upload to their folder" on storage.objects;
drop policy if exists "Users can update their own files" on storage.objects;
drop policy if exists "Users can delete their own files" on storage.objects;

-- Enhanced storage policies
create policy "Public can view user photos"
  on storage.objects for select 
  using (bucket_id = 'user-photos');

create policy "Authenticated users can upload to their folder"
  on storage.objects for insert to authenticated
  with check (
    bucket_id = 'user-photos'
    and auth.uid()::text = (storage.foldername(name))[1]
    and (storage.extension(name)) in ('jpg', 'jpeg', 'png', 'webp', 'gif')
  );

create policy "Users can update their own files"
  on storage.objects for update to authenticated 
  using (
    bucket_id = 'user-photos' 
    and auth.uid()::text = (storage.foldername(name))[1]
  )
  with check (
    bucket_id = 'user-photos' 
    and auth.uid()::text = (storage.foldername(name))[1]
  );

create policy "Users can delete their own files"
  on storage.objects for delete to authenticated 
  using (
    bucket_id = 'user-photos' 
    and auth.uid()::text = (storage.foldername(name))[1]
  );

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Beach locations indexes
create index if not exists beach_locations_slug_idx on public.beach_locations(slug);
create index if not exists beach_locations_is_active_idx on public.beach_locations(is_active);
create index if not exists beach_locations_is_featured_idx on public.beach_locations(is_featured);
create index if not exists beach_locations_location_idx on public.beach_locations(latitude, longitude);

-- Moto taxi drivers indexes
create index if not exists moto_taxi_drivers_is_available_idx on public.moto_taxi_drivers(is_available);
create index if not exists moto_taxi_drivers_is_verified_idx on public.moto_taxi_drivers(is_verified);
create index if not exists moto_taxi_drivers_vehicle_type_idx on public.moto_taxi_drivers(vehicle_type);

-- User photos indexes
create index if not exists user_photos_user_id_idx on public.user_photos(user_id);
create index if not exists user_photos_is_approved_idx on public.user_photos(is_approved);
create index if not exists user_photos_location_id_idx on public.user_photos(location_id);

-- Photo likes indexes
create index if not exists photo_likes_photo_id_idx on public.photo_likes(photo_id);
create index if not exists photo_likes_user_id_idx on public.photo_likes(user_id);

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to update updated_at timestamp
create or replace function public.update_updated_at_column()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

-- Triggers to automatically update updated_at
drop trigger if exists update_beach_locations_updated_at on public.beach_locations;
create trigger update_beach_locations_updated_at
  before update on public.beach_locations
  for each row execute procedure public.update_updated_at_column();

drop trigger if exists update_moto_taxi_drivers_updated_at on public.moto_taxi_drivers;
create trigger update_moto_taxi_drivers_updated_at
  before update on public.moto_taxi_drivers
  for each row execute procedure public.update_updated_at_column();

drop trigger if exists update_user_photos_updated_at on public.user_photos;
create trigger update_user_photos_updated_at
  before update on public.user_photos
  for each row execute procedure public.update_updated_at_column();
