-- Seed data for the application
-- This migration adds initial data to the database

-- ============================================================================
-- BEACH LOCATIONS SEED DATA
-- ============================================================================

-- Insert beach locations data
insert into public.beach_locations (
  slug, name, description, full_description, coordinates, latitude, longitude, 
  amenities, how_to_get, tips, is_featured
) values
(
  'praia-do-farol',
  'Praia do Farol',
  'Principal praia da ilha com farol histórico',
  'A Praia do Farol é a principal praia de Cotijuba, conhecida por seu farol histórico que serve como ponto de referência para navegadores. Com excelente infraestrutura, oferece restaurantes locais, banheiros limpos e amplas áreas de sombra natural.',
  '1°12''18"S 48°36''54"W',
  -1.205,
  -48.615,
  ARRAY['Restaurantes', 'Banheiros', 'Sombra natural'],
  'Localizada próximo ao porto principal da ilha, é facilmente acessível a pé ou de moto-táxi.',
  ARRAY['Melhor horário: manhã e final da tarde', 'Ideal para famílias', 'Tem estrutura para refeições'],
  true
),
(
  'praia-da-saudade',
  'Praia da Saudade',
  'Praia tranquila ideal para famílias',
  'Um refúgio de paz e tranquilidade, a Praia da Saudade é perfeita para famílias que buscam um ambiente mais reservado. Possui acesso a água doce natural e permite camping.',
  '1°13''05"S 48°37''41"W',
  -1.2181,
  -48.6281,
  ARRAY['Água doce', 'Área de camping', 'Trilhas'],
  'Acesso por trilha de aproximadamente 15 minutos a partir do centro da ilha.',
  ARRAY['Leve repelente', 'Ideal para camping', 'Água doce disponível'],
  true
),
(
  'praia-do-amor',
  'Praia do Amor',
  'Praia romântica com águas calmas',
  'Conhecida por seu ambiente romântico e pôr do sol espetacular, a Praia do Amor oferece águas calmas e rasas, perfeitas para relaxar. É um local mais reservado, ideal para casais.',
  '1°12''32"S 48°37''55"W',
  -1.2089,
  -48.6319,
  ARRAY['Privacidade', 'Pôr do sol', 'Águas rasas'],
  'Acesso por trilha curta de 10 minutos ou moto-táxi.',
  ARRAY['Melhor pôr do sol da ilha', 'Águas rasas e calmas', 'Ambiente romântico'],
  true
),
(
  'praia-da-flexeira',
  'Praia da Flexeira',
  'Praia com vegetação nativa preservada',
  'A Praia da Flexeira é um santuário natural com vegetação nativa preservada. Oferece trilhas ecológicas e é um dos melhores pontos para observação de aves na ilha.',
  '1°13''12"S 48°36''36"W',
  -1.22,
  -48.61,
  ARRAY['Natureza preservada', 'Trilhas ecológicas', 'Observação de aves'],
  'Acesso por trilha ecológica de 20 minutos, ideal para caminhadas.',
  ARRAY['Leve câmera para fotos', 'Ótima para trilhas', 'Rica em vida selvagem'],
  true
)
on conflict (slug) do nothing;

-- ============================================================================
-- MOTO TAXI DRIVERS SEED DATA
-- ============================================================================

-- Moto taxi drivers seed data moved to separate migration to handle constraints properly

-- ============================================================================
-- ADDITIONAL SECURITY FUNCTIONS
-- ============================================================================

-- Function to check if user is admin (placeholder for future admin system)
create or replace function public.is_admin(user_id uuid)
returns boolean as $$
begin
  -- For now, return false. In production, implement proper admin role checking
  return false;
end;
$$ language plpgsql security definer;

-- Function to sanitize user input
create or replace function public.sanitize_text(input_text text)
returns text as $$
begin
  -- Basic sanitization - remove potentially harmful characters
  return regexp_replace(
    regexp_replace(input_text, '[<>]', '', 'g'),
    '\s+', ' ', 'g'
  );
end;
$$ language plpgsql;

-- ============================================================================
-- AUDIT LOG TABLE (Optional - for production monitoring)
-- ============================================================================

-- Create audit log table
create table if not exists public.audit_log (
  id uuid primary key default gen_random_uuid(),
  table_name text not null,
  operation text not null,
  user_id uuid references auth.users(id),
  old_data jsonb,
  new_data jsonb,
  created_at timestamptz default now()
);

alter table public.audit_log enable row level security;

-- Only admins can view audit logs
drop policy if exists "Only admins can view audit logs" on public.audit_log;
create policy "Only admins can view audit logs"
  on public.audit_log for select 
  using (public.is_admin(auth.uid()));

-- Function to log changes
create or replace function public.audit_trigger()
returns trigger as $$
begin
  if TG_OP = 'DELETE' then
    insert into public.audit_log (table_name, operation, user_id, old_data)
    values (TG_TABLE_NAME, TG_OP, auth.uid(), to_jsonb(OLD));
    return OLD;
  elsif TG_OP = 'UPDATE' then
    insert into public.audit_log (table_name, operation, user_id, old_data, new_data)
    values (TG_TABLE_NAME, TG_OP, auth.uid(), to_jsonb(OLD), to_jsonb(NEW));
    return NEW;
  elsif TG_OP = 'INSERT' then
    insert into public.audit_log (table_name, operation, user_id, new_data)
    values (TG_TABLE_NAME, TG_OP, auth.uid(), to_jsonb(NEW));
    return NEW;
  end if;
  return null;
end;
$$ language plpgsql security definer;

-- ============================================================================
-- RATE LIMITING (Basic implementation)
-- ============================================================================

-- Create rate limiting table
create table if not exists public.rate_limits (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users(id),
  action text not null,
  count integer default 1,
  window_start timestamptz default now(),
  created_at timestamptz default now()
);

alter table public.rate_limits enable row level security;

-- Users can only see their own rate limits
drop policy if exists "Users can view own rate limits" on public.rate_limits;
create policy "Users can view own rate limits"
  on public.rate_limits for select to authenticated
  using (auth.uid() = user_id);

-- Function to check rate limits
create or replace function public.check_rate_limit(
  action_name text,
  max_requests integer default 10,
  window_minutes integer default 60
)
returns boolean as $$
declare
  current_count integer;
begin
  -- Clean up old entries
  delete from public.rate_limits 
  where window_start < now() - interval '1 hour' * window_minutes / 60;
  
  -- Get current count for this user and action
  select coalesce(sum(count), 0) into current_count
  from public.rate_limits
  where user_id = auth.uid()
    and action = action_name
    and window_start > now() - interval '1 minute' * window_minutes;
  
  -- If under limit, increment counter
  if current_count < max_requests then
    insert into public.rate_limits (user_id, action, count)
    values (auth.uid(), action_name, 1)
    on conflict (user_id, action) do update set
      count = rate_limits.count + 1,
      window_start = case 
        when rate_limits.window_start < now() - interval '1 minute' * window_minutes
        then now()
        else rate_limits.window_start
      end;
    return true;
  end if;
  
  return false;
end;
$$ language plpgsql security definer;
