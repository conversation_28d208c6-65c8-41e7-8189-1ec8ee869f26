#!/bin/bash

# Supabase Testing Script
# This script tests the Supabase setup and functionality

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_RUN=$((TESTS_RUN + 1))
    print_status "Running test: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        print_success "✓ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_error "✗ $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to test prerequisites
test_prerequisites() {
    print_status "Testing prerequisites..."
    
    run_test "Node.js is installed" "command -v node"
    run_test "npm is installed" "command -v npm"
    run_test "Supabase CLI is installed" "command -v supabase"
    
    # Test Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    run_test "Node.js version >= 18" "[ $NODE_VERSION -ge 18 ]"
}

# Function to test project structure
test_project_structure() {
    print_status "Testing project structure..."
    
    run_test "package.json exists" "[ -f package.json ]"
    run_test "supabase directory exists" "[ -d supabase ]"
    run_test "migrations directory exists" "[ -d supabase/migrations ]"
    run_test "config.toml exists" "[ -f supabase/config.toml ]"
    run_test "setup script exists" "[ -f scripts/setup-supabase.sh ]"
    run_test "setup script is executable" "[ -x scripts/setup-supabase.sh ]"
}

# Function to test Supabase connection
test_supabase_connection() {
    print_status "Testing Supabase connection..."
    
    # Test if project is linked
    if [ -f supabase/config.toml ]; then
        PROJECT_ID=$(grep "project_id" supabase/config.toml | cut -d'"' -f2)
        run_test "Project is linked" "[ '$PROJECT_ID' = 'exxscdqyluftxhtxdofq' ]"
    else
        print_error "✗ config.toml not found"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test migration list command
    run_test "Can list migrations" "supabase migration list"
}

# Function to test migrations
test_migrations() {
    print_status "Testing migrations..."
    
    # Check if migration files exist
    run_test "Schema migration exists" "[ -f supabase/migrations/20250822000000_consolidated_schema.sql ]"
    run_test "Seed data migration exists" "[ -f supabase/migrations/20250822000001_seed_data.sql ]"
    run_test "Fix migration exists" "[ -f supabase/migrations/20250822000002_fix_constraints.sql ]"
    
    # Test migration status
    MIGRATION_OUTPUT=$(supabase migration list 2>/dev/null || echo "")
    
    run_test "Schema migration applied" "echo '$MIGRATION_OUTPUT' | grep -q '20250822000000.*20250822000000'"
    run_test "Seed migration applied" "echo '$MIGRATION_OUTPUT' | grep -q '20250822000001.*20250822000001'"
    run_test "Fix migration applied" "echo '$MIGRATION_OUTPUT' | grep -q '20250822000002.*20250822000002'"
}

# Function to test TypeScript types
test_typescript_types() {
    print_status "Testing TypeScript types..."
    
    run_test "Types file exists" "[ -f src/integrations/supabase/types.ts ]"
    run_test "Types file is not empty" "[ -s src/integrations/supabase/types.ts ]"
    run_test "Types contain Database interface" "grep -q 'export type Database' src/integrations/supabase/types.ts"
    run_test "Types contain public schema" "grep -q 'public:' src/integrations/supabase/types.ts"
}

# Function to test environment setup
test_environment() {
    print_status "Testing environment setup..."
    
    run_test ".env.example exists" "[ -f .env.example ]"
    
    if [ -f .env ]; then
        run_test ".env file exists" "true"
        run_test ".env contains SUPABASE_URL" "grep -q 'VITE_SUPABASE_URL' .env"
        run_test ".env contains SUPABASE_ANON_KEY" "grep -q 'VITE_SUPABASE_ANON_KEY' .env"
    else
        print_warning "⚠ .env file not found (will be created during setup)"
    fi
}

# Function to test npm scripts
test_npm_scripts() {
    print_status "Testing npm scripts..."
    
    run_test "supabase:setup script exists" "npm run supabase:setup --help"
    run_test "supabase:verify script exists" "npm run supabase:verify --help"
    run_test "supabase:migrations script exists" "npm run supabase:migrations --help"
    run_test "supabase:types script exists" "npm run supabase:types --help"
    run_test "supabase:status script exists" "npm run supabase:status --help"
}

# Function to test client configuration
test_client_config() {
    print_status "Testing client configuration..."
    
    run_test "Supabase client file exists" "[ -f src/integrations/supabase/client.ts ]"
    run_test "Client exports supabase instance" "grep -q 'export const supabase' src/integrations/supabase/client.ts"
    run_test "Client uses correct URL" "grep -q 'exxscdqyluftxhtxdofq.supabase.co' src/integrations/supabase/client.ts"
}

# Function to test setup script functionality
test_setup_script() {
    print_status "Testing setup script functionality..."
    
    # Test help option
    run_test "Setup script shows help" "./scripts/setup-supabase.sh --help"
    
    # Test verify option
    run_test "Setup script verify works" "./scripts/setup-supabase.sh --verify"
    
    # Test migrations option
    run_test "Setup script migrations works" "./scripts/setup-supabase.sh --migrations"
}

# Function to run integration tests
test_integration() {
    print_status "Running integration tests..."
    
    # Test that we can generate types
    run_test "Can generate TypeScript types" "npm run supabase:types"
    
    # Test that we can check migration status
    run_test "Can check migration status" "npm run supabase:status"
    
    # Test verification script
    run_test "Verification script passes" "npm run supabase:verify"
}

# Function to print test summary
print_summary() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "              Test Summary                        "
    echo "=================================================="
    echo -e "${NC}"
    
    echo "Tests run: $TESTS_RUN"
    echo -e "${GREEN}Tests passed: $TESTS_PASSED${NC}"
    
    if [ $TESTS_FAILED -gt 0 ]; then
        echo -e "${RED}Tests failed: $TESTS_FAILED${NC}"
        echo ""
        print_error "Some tests failed. Please check the output above."
        return 1
    else
        echo -e "${GREEN}Tests failed: $TESTS_FAILED${NC}"
        echo ""
        print_success "All tests passed! ✨"
        return 0
    fi
}

# Main test function
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "       Supabase Testing Script for Cotiju App    "
    echo "=================================================="
    echo -e "${NC}"
    
    test_prerequisites
    test_project_structure
    test_environment
    test_supabase_connection
    test_migrations
    test_typescript_types
    test_npm_scripts
    test_client_config
    test_setup_script
    test_integration
    
    print_summary
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Supabase Testing Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h         Show this help message"
        echo "  --prerequisites    Test only prerequisites"
        echo "  --structure        Test only project structure"
        echo "  --connection       Test only Supabase connection"
        echo "  --migrations       Test only migrations"
        echo "  --integration      Test only integration"
        echo ""
        echo "This script will test:"
        echo "  1. Prerequisites (Node.js, npm, Supabase CLI)"
        echo "  2. Project structure and files"
        echo "  3. Environment configuration"
        echo "  4. Supabase connection and project linking"
        echo "  5. Database migrations"
        echo "  6. TypeScript type generation"
        echo "  7. npm scripts functionality"
        echo "  8. Client configuration"
        echo "  9. Setup script functionality"
        echo "  10. Integration tests"
        exit 0
        ;;
    --prerequisites)
        test_prerequisites
        print_summary
        ;;
    --structure)
        test_project_structure
        print_summary
        ;;
    --connection)
        test_supabase_connection
        print_summary
        ;;
    --migrations)
        test_migrations
        print_summary
        ;;
    --integration)
        test_integration
        print_summary
        ;;
    *)
        main
        ;;
esac
