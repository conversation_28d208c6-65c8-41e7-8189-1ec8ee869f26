import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Your Supabase configuration
const SUPABASE_URL = 'https://exxscdqyluftxhtxdofq.supabase.co';
const SUPABASE_SERVICE_KEY = 'your-service-role-key'; // Use service role key, not anon key

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function runMigrations() {
  try {
    console.log('Starting migration process...');
    
    // Get all migration files
    const migrationsDir = path.join(__dirname, '../supabase/migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to ensure proper order
    
    console.log(`Found ${migrationFiles.length} migration files`);
    
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      
      const migrationPath = path.join(migrationsDir, file);
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      
      // Split by semicolon and filter out empty statements
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);
      
      // Execute each statement
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        if (statement) {
          try {
            const { error } = await supabase.rpc('exec_sql', { 
              sql: statement + ';' 
            });
            
            if (error) {
              console.error(`Error in ${file}, statement ${i + 1}:`, error);
              throw error;
            }
          } catch (err) {
            // Try direct query if RPC fails
            const { error } = await supabase
              .from('_migrations')
              .select('*')
              .limit(1);
            
            if (error && error.code === '42P01') {
              // Table doesn't exist, use raw SQL
              console.log('Using raw SQL execution...');
              // Note: This requires a custom function or direct database access
            }
          }
        }
      }
      
      console.log(`✅ Completed migration: ${file}`);
    }
    
    console.log('🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Alternative approach using direct SQL execution
async function runMigrationsDirectSQL() {
  try {
    console.log('Starting migration process with direct SQL...');
    
    const migrationsDir = path.join(__dirname, '../supabase/migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      
      const migrationPath = path.join(migrationsDir, file);
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      
      // Execute the entire migration file
      const { error } = await supabase.rpc('exec_migration', {
        migration_sql: migrationSQL
      });
      
      if (error) {
        console.error(`Error running ${file}:`, error);
        throw error;
      }
      
      console.log(`✅ Completed migration: ${file}`);
    }
    
    console.log('🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migrations
runMigrations();
