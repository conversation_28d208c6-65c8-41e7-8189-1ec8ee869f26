import pg from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const { Client } = pg;

// Your database connection string (get this from Supabase settings)
const DATABASE_URL = 'postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres';

async function runMigrationsWithPostgres() {
  const client = new Client({
    connectionString: DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Create migrations tracking table if it doesn't exist
    await client.query(`
      CREATE TABLE IF NOT EXISTS _migrations (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Get all migration files
    const migrationsDir = path.join(__dirname, '../supabase/migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    console.log(`Found ${migrationFiles.length} migration files`);

    for (const file of migrationFiles) {
      // Check if migration has already been run
      const { rows } = await client.query(
        'SELECT filename FROM _migrations WHERE filename = $1',
        [file]
      );

      if (rows.length > 0) {
        console.log(`⏭️  Skipping already executed migration: ${file}`);
        continue;
      }

      console.log(`🔄 Running migration: ${file}`);

      const migrationPath = path.join(migrationsDir, file);
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

      // Begin transaction
      await client.query('BEGIN');

      try {
        // Execute migration
        await client.query(migrationSQL);

        // Record migration as executed
        await client.query(
          'INSERT INTO _migrations (filename) VALUES ($1)',
          [file]
        );

        // Commit transaction
        await client.query('COMMIT');
        console.log(`✅ Completed migration: ${file}`);

      } catch (error) {
        // Rollback on error
        await client.query('ROLLBACK');
        console.error(`❌ Error in migration ${file}:`, error.message);
        throw error;
      }
    }

    console.log('🎉 All migrations completed successfully!');

  } catch (error) {
    console.error('❌ Migration process failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run migrations
runMigrationsWithPostgres();
