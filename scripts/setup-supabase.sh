#!/bin/bash

# Supabase Setup Script
# This script sets up the entire Supabase environment with one command

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists supabase; then
        print_error "Supabase CLI is not installed. Please install it first:"
        print_error "npm install -g supabase"
        exit 1
    fi
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Function to setup environment variables
setup_env() {
    print_status "Setting up environment variables..."
    
    if [ ! -f .env ]; then
        print_status "Creating .env file..."
        cat > .env << EOF
# Supabase Configuration
VITE_SUPABASE_URL=https://exxscdqyluftxhtxdofq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4eHNjZHF5bHVmdHhodHhkb2ZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4NzEwMzksImV4cCI6MjA3MDQ0NzAzOX0.2wsYufp3xqO1gUY08fT5VhXxH3z4X1Ihpm6C1IPvetk

# Development
NODE_ENV=development
EOF
        print_success "Created .env file with Supabase configuration"
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Function to link to Supabase project
link_project() {
    print_status "Linking to Supabase project..."
    
    # Check if already linked
    if [ -f supabase/config.toml ]; then
        PROJECT_ID=$(grep "project_id" supabase/config.toml | cut -d'"' -f2)
        if [ "$PROJECT_ID" = "exxscdqyluftxhtxdofq" ]; then
            print_success "Already linked to Supabase project"
            return 0
        fi
    fi
    
    # Link to the project
    supabase link --project-ref exxscdqyluftxhtxdofq
    print_success "Linked to Supabase project"
}

# Function to run migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Check migration status
    print_status "Checking migration status..."
    supabase migration list
    
    # Push any pending migrations
    print_status "Pushing migrations to remote database..."
    supabase db push --include-all
    
    print_success "All migrations applied successfully"
}

# Function to verify setup
verify_setup() {
    print_status "Verifying setup..."

    # Check migration status
    print_status "Checking migration status..."
    MIGRATION_OUTPUT=$(supabase migration list 2>/dev/null || echo "")

    if echo "$MIGRATION_OUTPUT" | grep -q "20250822000000"; then
        print_success "✓ Schema migration applied"
    else
        print_error "✗ Schema migration not applied"
    fi

    if echo "$MIGRATION_OUTPUT" | grep -q "20250822000001"; then
        print_success "✓ Seed data migration applied"
    else
        print_error "✗ Seed data migration not applied"
    fi

    if echo "$MIGRATION_OUTPUT" | grep -q "20250822000002"; then
        print_success "✓ Constraint fix migration applied"
    else
        print_error "✗ Constraint fix migration not applied"
    fi

    # Check if project is linked
    if [ -f supabase/config.toml ]; then
        PROJECT_ID=$(grep "project_id" supabase/config.toml | cut -d'"' -f2)
        if [ "$PROJECT_ID" = "exxscdqyluftxhtxdofq" ]; then
            print_success "✓ Project linked to Supabase"
        else
            print_error "✗ Project not properly linked"
        fi
    else
        print_error "✗ Supabase config missing"
    fi

    # Check if environment file exists
    if [ -f .env ]; then
        print_success "✓ Environment file exists"
    else
        print_warning "⚠ Environment file missing (will be created during setup)"
    fi

    # Check if TypeScript types exist
    if [ -f src/integrations/supabase/types.ts ]; then
        print_success "✓ TypeScript types generated"
    else
        print_warning "⚠ TypeScript types missing (will be generated during setup)"
    fi

    print_success "Setup verification completed"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    if [ -f package-lock.json ]; then
        npm ci
    else
        npm install
    fi
    
    print_success "Dependencies installed successfully"
}

# Function to generate types
generate_types() {
    print_status "Generating TypeScript types from database schema..."
    
    supabase gen types typescript --project-id exxscdqyluftxhtxdofq > src/integrations/supabase/types.ts
    
    print_success "TypeScript types generated successfully"
}

# Main setup function
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "       Supabase Setup Script for Cotiju App      "
    echo "=================================================="
    echo -e "${NC}"
    
    check_prerequisites
    setup_env
    install_dependencies
    link_project
    run_migrations
    generate_types
    verify_setup
    
    echo -e "${GREEN}"
    echo "=================================================="
    echo "           Setup completed successfully!          "
    echo "=================================================="
    echo -e "${NC}"
    
    print_success "Your Supabase environment is now ready!"
    print_status "You can now run 'npm run dev' to start the development server"
    print_status "Database URL: https://exxscdqyluftxhtxdofq.supabase.co"
    print_status "Dashboard: https://supabase.com/dashboard/project/exxscdqyluftxhtxdofq"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Supabase Setup Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --verify       Only run verification checks"
        echo "  --migrations   Only run migrations"
        echo ""
        echo "This script will:"
        echo "  1. Check prerequisites (Supabase CLI, Node.js, npm)"
        echo "  2. Setup environment variables"
        echo "  3. Install project dependencies"
        echo "  4. Link to Supabase project"
        echo "  5. Run database migrations"
        echo "  6. Generate TypeScript types"
        echo "  7. Verify the setup"
        exit 0
        ;;
    --verify)
        check_prerequisites
        verify_setup
        exit 0
        ;;
    --migrations)
        check_prerequisites
        link_project
        run_migrations
        exit 0
        ;;
    *)
        main
        ;;
esac
