/**
 * Supabase Integration Tests
 * 
 * These tests verify that the Supabase integration is working correctly.
 * They test authentication, database operations, and RLS policies.
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { supabase } from '../integrations/supabase/client'
import type { Database } from '../integrations/supabase/types'

// Test user credentials for testing
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123'
}

describe('Supabase Integration', () => {
  let testUserId: string | null = null

  beforeAll(async () => {
    // Clean up any existing test user
    try {
      await supabase.auth.signOut()
    } catch (error) {
      // Ignore errors during cleanup
    }
  })

  afterAll(async () => {
    // Clean up test data
    if (testUserId) {
      try {
        // Delete test user's data
        await supabase.from('user_photos').delete().eq('user_id', testUserId)
        await supabase.from('profiles').delete().eq('id', testUserId)
        await supabase.auth.signOut()
      } catch (error) {
        console.warn('Cleanup failed:', error)
      }
    }
  })

  describe('Connection and Configuration', () => {
    it('should have valid Supabase configuration', () => {
      expect(supabase).toBeDefined()
      expect(supabase.supabaseUrl).toContain('exxscdqyluftxhtxdofq.supabase.co')
      expect(supabase.supabaseKey).toBeDefined()
    })

    it('should be able to connect to the database', async () => {
      const { data, error } = await supabase.from('beach_locations').select('count').limit(1)
      expect(error).toBeNull()
      expect(data).toBeDefined()
    })
  })

  describe('Public Data Access', () => {
    it('should be able to read beach locations without authentication', async () => {
      const { data, error } = await supabase
        .from('beach_locations')
        .select('id, name, slug, is_active')
        .eq('is_active', true)
        .limit(5)

      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(Array.isArray(data)).toBe(true)
    })

    it('should be able to read active moto taxi drivers without authentication', async () => {
      const { data, error } = await supabase
        .from('moto_taxi_drivers')
        .select('id, name, phone, vehicle_type, is_available')
        .eq('is_available', true)
        .limit(5)

      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(Array.isArray(data)).toBe(true)
    })

    it('should be able to read approved photos without authentication', async () => {
      const { data, error } = await supabase
        .from('user_photos')
        .select('id, image_url, caption, is_approved')
        .eq('is_approved', true)
        .limit(5)

      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(Array.isArray(data)).toBe(true)
    })
  })

  describe('Authentication', () => {
    it('should be able to sign up a new user', async () => {
      const { data, error } = await supabase.auth.signUp({
        email: TEST_USER.email,
        password: TEST_USER.password,
        options: {
          data: {
            name: 'Test User'
          }
        }
      })

      expect(error).toBeNull()
      expect(data.user).toBeDefined()
      
      if (data.user) {
        testUserId = data.user.id
      }
    })

    it('should be able to sign in with valid credentials', async () => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: TEST_USER.email,
        password: TEST_USER.password
      })

      expect(error).toBeNull()
      expect(data.user).toBeDefined()
      expect(data.session).toBeDefined()
      
      if (data.user) {
        testUserId = data.user.id
      }
    })

    it('should create a profile automatically on signup', async () => {
      if (!testUserId) {
        throw new Error('Test user not created')
      }

      // Wait a bit for the trigger to execute
      await new Promise(resolve => setTimeout(resolve, 1000))

      const { data, error } = await supabase
        .from('profiles')
        .select('id, display_name')
        .eq('id', testUserId)
        .single()

      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(data?.id).toBe(testUserId)
    })
  })

  describe('Row Level Security (RLS)', () => {
    it('should allow authenticated users to read their own profile', async () => {
      if (!testUserId) {
        throw new Error('Test user not authenticated')
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', testUserId)
        .single()

      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(data?.id).toBe(testUserId)
    })

    it('should allow authenticated users to update their own profile', async () => {
      if (!testUserId) {
        throw new Error('Test user not authenticated')
      }

      const { data, error } = await supabase
        .from('profiles')
        .update({ display_name: 'Updated Test User' })
        .eq('id', testUserId)
        .select()
        .single()

      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(data?.display_name).toBe('Updated Test User')
    })

    it('should prevent unauthenticated users from inserting photos', async () => {
      // Sign out first
      await supabase.auth.signOut()

      const { data, error } = await supabase
        .from('user_photos')
        .insert({
          user_id: testUserId || 'fake-id',
          image_url: 'https://example.com/test.jpg',
          caption: 'Test photo'
        })

      expect(error).not.toBeNull()
      expect(data).toBeNull()

      // Sign back in for other tests
      if (testUserId) {
        await supabase.auth.signInWithPassword({
          email: TEST_USER.email,
          password: TEST_USER.password
        })
      }
    })
  })

  describe('Storage', () => {
    it('should have user-photos bucket configured', async () => {
      const { data, error } = await supabase.storage.listBuckets()

      expect(error).toBeNull()
      expect(data).toBeDefined()
      
      const userPhotosBucket = data?.find(bucket => bucket.name === 'user-photos')
      expect(userPhotosBucket).toBeDefined()
      expect(userPhotosBucket?.public).toBe(true)
    })

    it('should allow authenticated users to upload to their folder', async () => {
      if (!testUserId) {
        throw new Error('Test user not authenticated')
      }

      // Create a simple test file
      const testFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      const filePath = `${testUserId}/test-${Date.now()}.jpg`

      const { data, error } = await supabase.storage
        .from('user-photos')
        .upload(filePath, testFile)

      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(data?.path).toBe(filePath)

      // Clean up the test file
      await supabase.storage.from('user-photos').remove([filePath])
    })
  })

  describe('Database Functions', () => {
    it('should have required tables with correct structure', async () => {
      // Test that we can query each table (which means they exist and have proper RLS)
      const tables = [
        'profiles',
        'moto_taxi_drivers', 
        'beach_locations',
        'user_photos',
        'photo_likes'
      ]

      for (const table of tables) {
        const { error } = await supabase.from(table).select('*').limit(1)
        expect(error).toBeNull()
      }
    })

    it('should have proper indexes for performance', async () => {
      // Test that queries on indexed columns are fast
      const start = Date.now()
      
      const { error } = await supabase
        .from('beach_locations')
        .select('*')
        .eq('is_active', true)
        .limit(10)

      const duration = Date.now() - start
      
      expect(error).toBeNull()
      expect(duration).toBeLessThan(1000) // Should be fast with proper indexing
    })
  })

  describe('Real-time Features', () => {
    it('should be able to subscribe to real-time changes', async () => {
      let receivedUpdate = false
      
      const channel = supabase
        .channel('test-channel')
        .on('postgres_changes', 
          { 
            event: 'INSERT', 
            schema: 'public', 
            table: 'beach_locations' 
          }, 
          () => {
            receivedUpdate = true
          }
        )
        .subscribe()

      expect(channel).toBeDefined()
      
      // Clean up
      await supabase.removeChannel(channel)
    })
  })
})
