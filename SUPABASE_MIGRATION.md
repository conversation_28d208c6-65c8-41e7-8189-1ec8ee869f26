# Supabase Migration Guide

This guide provides comprehensive instructions for setting up and managing the Supabase backend for the Cotiju App tourism application.

## Table of Contents

- [Quick Start (One Command Setup)](#quick-start-one-command-setup)
- [Prerequisites](#prerequisites)
- [Manual Setup Process](#manual-setup-process)
- [Database Schema](#database-schema)
- [Migration Management](#migration-management)
- [Troubleshooting](#troubleshooting)
- [Development vs Production](#development-vs-production)
- [Available Scripts](#available-scripts)

## Quick Start (One Command Setup)

For the fastest setup, use our automated setup script:

```bash
npm run supabase:setup
```

This single command will:
1. Check all prerequisites
2. Install project dependencies
3. Setup environment variables
4. Link to the Supabase project
5. Run all database migrations
6. Generate TypeScript types
7. Verify the complete setup

## Prerequisites

Before starting, ensure you have the following installed:

### Required Software

1. **Node.js** (v18 or higher)
   ```bash
   node --version
   ```

2. **npm** (comes with Node.js)
   ```bash
   npm --version
   ```

3. **Supabase CLI**
   ```bash
   npm install -g supabase
   ```

### Supabase Project Access

- Project ID: `exxscdqyluftxhtxdofq`
- Project URL: `https://exxscdqyluftxhtxdofq.supabase.co`
- Dashboard: `https://supabase.com/dashboard/project/exxscdqyluftxhtxdofq`

## Manual Setup Process

If you prefer to set up manually or need to troubleshoot, follow these steps:

### Step 1: Install Dependencies

```bash
npm install
```

### Step 2: Setup Environment Variables

Create a `.env` file in the project root:

```bash
cp .env.example .env
```

The `.env` file should contain:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://exxscdqyluftxhtxdofq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4eHNjZHF5bHVmdHhodHhkb2ZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4NzEwMzksImV4cCI6MjA3MDQ0NzAzOX0.2wsYufp3xqO1gUY08fT5VhXxH3z4X1Ihpm6C1IPvetk

# Development
NODE_ENV=development
```

### Step 3: Link to Supabase Project

```bash
npm run supabase:link
```

### Step 4: Run Migrations

```bash
npm run supabase:push
```

### Step 5: Generate TypeScript Types

```bash
npm run supabase:types
```

### Step 6: Verify Setup

```bash
npm run supabase:verify
```

## Database Schema

The application uses the following database structure:

### Core Tables

1. **profiles** - User profile information
   - Links to `auth.users` table
   - Stores display name, avatar URL, and member since date
   - Automatically created when users sign up

2. **moto_taxi_drivers** - Driver directory
   - Driver information including contact details
   - Vehicle information and capacity
   - Availability status and ratings
   - Service areas and pricing

3. **beach_locations** - Beach and location data
   - Location details with coordinates
   - Amenities and access information
   - Featured locations for the app

4. **user_photos** - Photo sharing system
   - User-uploaded photos with approval system
   - Location tagging and categorization
   - Like counting and featured photos

5. **photo_likes** - Photo like system
   - Tracks user likes on photos
   - Prevents duplicate likes per user

### Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Authentication policies** ensure users can only access their own data
- **Storage policies** for secure file uploads
- **Rate limiting** to prevent abuse
- **Audit logging** for production monitoring

### Storage

- **user-photos** bucket for photo uploads
- File type restrictions (jpg, jpeg, png, webp, gif)
- User-specific folder structure
- Public read access with authenticated write

## Migration Management

### Current Migrations

1. `20250822000000_consolidated_schema.sql` - Main schema setup
2. `20250822000001_seed_data.sql` - Initial data and security functions
3. `20250822000002_fix_constraints.sql` - Constraint fixes and driver data

### Migration Commands

```bash
# Check migration status
npm run supabase:status

# Push new migrations
npm run supabase:push

# Pull remote changes
npm run supabase:pull

# Run only migrations
npm run supabase:migrations
```

### Creating New Migrations

1. Create a new migration file:
   ```bash
   supabase migration new your_migration_name
   ```

2. Edit the generated SQL file in `supabase/migrations/`

3. Test the migration:
   ```bash
   npm run supabase:push
   ```

4. Verify the changes:
   ```bash
   npm run supabase:verify
   ```

## Troubleshooting

### Common Issues

#### 1. Migration History Mismatch

**Error**: "The remote database's migration history does not match local files"

**Solution**:
```bash
# Check which migrations need repair
supabase migration list

# Repair specific migrations (replace with actual migration IDs)
supabase migration repair --status applied MIGRATION_ID
```

#### 2. Permission Denied

**Error**: "permission denied for schema public"

**Solution**: Ensure you're using the correct project credentials and have proper access.

#### 3. Connection Issues

**Error**: "failed to connect to database"

**Solutions**:
- Check your internet connection
- Verify the project ID is correct
- Ensure the Supabase project is active

#### 4. TypeScript Type Errors

**Error**: Type errors in the application

**Solution**:
```bash
npm run supabase:types
```

### Debug Mode

For detailed error information, add `--debug` to any Supabase command:

```bash
supabase db push --debug
```

### Reset Database (Caution)

⚠️ **Warning**: This will delete all data!

```bash
supabase db reset
```

## Development vs Production

### Development Setup

- Uses local environment variables
- Direct connection to Supabase project
- Full access to dashboard and logs
- Automatic type generation

### Production Considerations

1. **Environment Variables**
   - Use production Supabase keys
   - Set `NODE_ENV=production`
   - Configure proper redirect URLs

2. **Security**
   - Enable email confirmations
   - Configure proper CORS settings
   - Set up proper rate limiting
   - Enable audit logging

3. **Performance**
   - Monitor database performance
   - Set up proper indexes
   - Configure connection pooling
   - Enable caching where appropriate

## Available Scripts

| Script | Description |
|--------|-------------|
| `npm run supabase:setup` | Complete one-command setup |
| `npm run supabase:verify` | Verify current setup |
| `npm run supabase:migrations` | Run migrations only |
| `npm run supabase:types` | Generate TypeScript types |
| `npm run supabase:link` | Link to Supabase project |
| `npm run supabase:push` | Push migrations to remote |
| `npm run supabase:pull` | Pull remote changes |
| `npm run supabase:status` | Check migration status |

## Support

For additional help:

1. Check the [Supabase Documentation](https://supabase.com/docs)
2. Review the project's existing integration documentation
3. Check the Supabase Dashboard for real-time logs
4. Use the `--help` flag with any script for more options

## Next Steps

After successful setup:

1. Start the development server: `npm run dev`
2. Test the authentication flow
3. Verify real-time features are working
4. Test photo upload functionality
5. Check that all RLS policies are working correctly

The application should now be fully functional with a complete Supabase backend!
